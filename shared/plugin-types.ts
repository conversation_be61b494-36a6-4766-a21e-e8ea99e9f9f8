import { ComponentType } from 'react';

/**
 * 插件元数据接口
 */
export interface PluginMetadata {
  /** 插件唯一标识符 */
  id: string;
  /** 插件名称 */
  name: string;
  /** 插件版本 */
  version: string;
  /** 插件描述 */
  description?: string;
  /** 插件作者 */
  author?: string;
  /** 插件依赖的主应用最低版本 */
  minAppVersion?: string;
}

/**
 * 插件上下文接口 - 主应用提供给插件的API
 */
export interface PluginContext {
  /** 应用版本 */
  appVersion: string;
  /** 注册菜单项 */
  registerMenuItem: (item: MenuItem) => void;
  /** 注册路由 */
  registerRoute: (route: PluginRoute) => void;
  /** 显示通知 */
  showNotification: (message: string, type?: 'info' | 'success' | 'warning' | 'error') => void;
  /** 获取应用状态 */
  getAppState: () => any;
  /** 设置应用状态 */
  setAppState: (state: any) => void;
}

/**
 * 菜单项接口
 */
export interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  onClick?: () => void;
  href?: string;
  children?: MenuItem[];
}

/**
 * 插件路由接口
 */
export interface PluginRoute {
  path: string;
  component: ComponentType<any>;
  exact?: boolean;
}

/**
 * 插件生命周期接口
 */
export interface PluginLifecycle {
  /** 插件激活时调用 */
  onActivate?: (context: PluginContext) => void | Promise<void>;
  /** 插件停用时调用 */
  onDeactivate?: () => void | Promise<void>;
  /** 插件卸载时调用 */
  onUnload?: () => void | Promise<void>;
}

/**
 * 插件主接口
 */
export interface Plugin extends PluginLifecycle {
  /** 插件元数据 */
  metadata: PluginMetadata;
  /** 插件主组件（可选） */
  component?: ComponentType<any>;
}

/**
 * 插件加载器接口
 */
export interface PluginLoader {
  /** 加载插件 */
  loadPlugin: (url: string) => Promise<Plugin>;
  /** 卸载插件 */
  unloadPlugin: (pluginId: string) => Promise<void>;
  /** 获取已加载的插件列表 */
  getLoadedPlugins: () => Plugin[];
  /** 检查插件是否已加载 */
  isPluginLoaded: (pluginId: string) => boolean;
}

/**
 * 插件配置接口
 */
export interface PluginConfig {
  /** 插件URL */
  url: string;
  /** 是否自动激活 */
  autoActivate?: boolean;
  /** 插件配置参数 */
  config?: Record<string, any>;
}
