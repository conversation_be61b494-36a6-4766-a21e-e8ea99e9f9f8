#!/usr/bin/env node

/**
 * 测试插件系统的脚本
 */

import { execSync } from 'child_process';

console.log('🧪 Testing Plugin System...\n');

// 测试主应用是否运行
console.log('1. Testing main application...');
try {
  const response = execSync('curl -s http://localhost:3000', { encoding: 'utf8' });
  if (response.includes('<!doctype html>')) {
    console.log('✅ Main application is running');
  } else {
    console.log('❌ Main application is not responding correctly');
  }
} catch (error) {
  console.log('❌ Main application is not accessible');
}

// 测试插件文件是否可访问
console.log('\n2. Testing plugin file accessibility...');
try {
  const pluginResponse = execSync('curl -s http://localhost:3001/dist/example-plugin.js', { encoding: 'utf8' });
  if (pluginResponse.includes('import') && pluginResponse.includes('react')) {
    console.log('✅ Plugin file is accessible and contains expected content');
  } else {
    console.log('❌ Plugin file is not accessible or content is incorrect');
  }
} catch (error) {
  console.log('❌ Plugin server is not accessible');
}

// 测试插件服务器首页
console.log('\n3. Testing plugin server index...');
try {
  const indexResponse = execSync('curl -s http://localhost:3001/', { encoding: 'utf8' });
  if (indexResponse.includes('Plugin Server')) {
    console.log('✅ Plugin server index is working');
  } else {
    console.log('❌ Plugin server index is not working');
  }
} catch (error) {
  console.log('❌ Plugin server index is not accessible');
}

// 检查构建文件
console.log('\n4. Checking build files...');
try {
  execSync('ls -la packages/main-app/dist/ 2>/dev/null || echo "Main app not built"');
  execSync('ls -la packages/example-plugin/dist/');
  console.log('✅ Plugin build files exist');
} catch (error) {
  console.log('❌ Build files are missing');
}

console.log('\n🎉 Plugin System Test Complete!');
console.log('\n📋 Next Steps:');
console.log('1. Open http://localhost:3000 in your browser');
console.log('2. Navigate to the "Plugins" page');
console.log('3. Load the plugin using URL: http://localhost:3001/dist/example-plugin.js');
console.log('4. Activate the plugin to see it in action');
console.log('\n💡 Features to test:');
console.log('- Plugin loading and activation');
console.log('- Plugin routes and navigation');
console.log('- Plugin components and state management');
console.log('- Shared React dependencies (no duplication)');
console.log('- Plugin lifecycle methods');
console.log('- Notification system');
