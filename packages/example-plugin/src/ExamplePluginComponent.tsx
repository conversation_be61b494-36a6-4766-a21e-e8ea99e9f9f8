import React, { useState, useEffect } from 'react';
import './ExamplePlugin.css';

/**
 * 示例插件组件
 */
export const ExamplePluginComponent: React.FC = () => {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('');
  const [todos, setTodos] = useState<string[]>([]);
  const [newTodo, setNewTodo] = useState('');

  useEffect(() => {
    setMessage('Example Plugin loaded successfully!');
  }, []);

  const addTodo = () => {
    if (newTodo.trim()) {
      setTodos([...todos, newTodo.trim()]);
      setNewTodo('');
    }
  };

  const removeTodo = (index: number) => {
    setTodos(todos.filter((_, i) => i !== index));
  };

  return (
    <div className="example-plugin">
      <div className="plugin-header">
        <h2>🔌 Example Plugin</h2>
        <p className="plugin-description">
          This is a demonstration plugin that shows how plugins can be dynamically loaded
          and integrated into the main application.
        </p>
      </div>

      <div className="plugin-content">
        {/* 状态消息 */}
        <div className="status-section">
          <h3>Status</h3>
          <p className="status-message">{message}</p>
          <p className="plugin-info">
            Plugin ID: <code>example-plugin</code><br />
            Version: <code>1.0.0</code><br />
            React Version: <code>{React.version}</code>
          </p>
        </div>

        {/* 计数器功能 */}
        <div className="counter-section">
          <h3>Counter Demo</h3>
          <div className="counter">
            <button onClick={() => setCount(count - 1)}>-</button>
            <span className="count-display">{count}</span>
            <button onClick={() => setCount(count + 1)}>+</button>
          </div>
          <button 
            className="reset-button"
            onClick={() => setCount(0)}
          >
            Reset
          </button>
        </div>

        {/* Todo 列表功能 */}
        <div className="todo-section">
          <h3>Todo List Demo</h3>
          <div className="todo-input">
            <input
              type="text"
              value={newTodo}
              onChange={(e) => setNewTodo(e.target.value)}
              placeholder="Enter a new todo..."
              onKeyPress={(e) => e.key === 'Enter' && addTodo()}
            />
            <button onClick={addTodo}>Add</button>
          </div>
          
          {todos.length > 0 && (
            <ul className="todo-list">
              {todos.map((todo, index) => (
                <li key={index} className="todo-item">
                  <span className="todo-text">{todo}</span>
                  <button 
                    className="remove-button"
                    onClick={() => removeTodo(index)}
                  >
                    ×
                  </button>
                </li>
              ))}
            </ul>
          )}
          
          {todos.length === 0 && (
            <p className="empty-todos">No todos yet. Add one above!</p>
          )}
        </div>

        {/* 插件功能演示 */}
        <div className="features-section">
          <h3>Plugin Features</h3>
          <ul className="features-list">
            <li>✅ Dynamic loading from URL</li>
            <li>✅ React components with state</li>
            <li>✅ Shared React dependency (no duplication)</li>
            <li>✅ ESM module format</li>
            <li>✅ TypeScript support</li>
            <li>✅ Hot reload during development</li>
            <li>✅ Plugin lifecycle management</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
