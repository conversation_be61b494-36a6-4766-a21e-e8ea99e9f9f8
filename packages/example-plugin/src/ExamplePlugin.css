.example-plugin {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-plugin .plugin-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #646cff;
}

.example-plugin .plugin-header h2 {
  color: #646cff;
  margin: 0 0 1rem 0;
  font-size: 2rem;
}

.example-plugin .plugin-description {
  color: #888;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

.example-plugin .plugin-content > div {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.example-plugin .plugin-content h3 {
  color: #fff;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.example-plugin .status-message {
  color: #4caf50;
  font-weight: bold;
  margin-bottom: 1rem;
}

.example-plugin .plugin-info {
  color: #ccc;
  font-size: 0.9rem;
  line-height: 1.6;
}

.example-plugin .plugin-info code {
  background: #333;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  color: #61dafb;
}

.example-plugin .counter {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.example-plugin .counter button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #646cff;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.example-plugin .counter button:hover {
  background: #535bf2;
}

.example-plugin .count-display {
  font-size: 2rem;
  font-weight: bold;
  color: #fff;
  min-width: 60px;
  text-align: center;
}

.example-plugin .reset-button {
  padding: 0.5rem 1rem;
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.example-plugin .reset-button:hover {
  background: #ff5252;
}

.example-plugin .todo-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.example-plugin .todo-input input {
  flex: 1;
  padding: 0.75rem;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 1rem;
}

.example-plugin .todo-input input:focus {
  outline: none;
  border-color: #646cff;
}

.example-plugin .todo-input button {
  padding: 0.75rem 1.5rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.example-plugin .todo-input button:hover {
  background: #535bf2;
}

.example-plugin .todo-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.example-plugin .todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #333;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.example-plugin .todo-text {
  color: #fff;
  flex: 1;
}

.example-plugin .remove-button {
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.example-plugin .remove-button:hover {
  background: #ff5252;
}

.example-plugin .empty-todos {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.example-plugin .features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.example-plugin .features-list li {
  color: #ccc;
  padding: 0.5rem 0;
  font-size: 1rem;
}
