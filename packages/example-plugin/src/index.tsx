import React from 'react';
import type { Plugin, PluginContext } from '@shared/plugin-types';
import { ExamplePluginComponent } from './ExamplePluginComponent';

/**
 * 示例插件实现
 */
const ExamplePlugin: Plugin = {
  metadata: {
    id: 'example-plugin',
    name: 'Example Plugin',
    version: '1.0.0',
    description: 'A demonstration plugin showing various features of the plugin system',
    author: 'Plugin Playground Team',
    minAppVersion: '1.0.0',
  },

  // 插件主组件
  component: ExamplePluginComponent,

  // 插件激活时调用
  onActivate: async (context: PluginContext) => {
    console.log('Example Plugin: onActivate called');
    
    // 注册菜单项
    context.registerMenuItem({
      id: 'example-plugin-menu',
      label: '🔌 Example Plugin',
      href: '/plugin/example',
    });

    // 注册路由
    context.registerRoute({
      path: '/plugin/example',
      component: ExamplePluginComponent,
    });

    // 显示激活通知
    context.showNotification(
      `${ExamplePlugin.metadata.name} has been activated!`,
      'success'
    );

    // 设置插件状态到应用状态中
    context.setAppState({
      [`plugin_${ExamplePlugin.metadata.id}_active`]: true,
      [`plugin_${ExamplePlugin.metadata.id}_activatedAt`]: new Date().toISOString(),
    });

    console.log('Example Plugin: Activation complete');
  },

  // 插件停用时调用
  onDeactivate: async () => {
    console.log('Example Plugin: onDeactivate called');
    
    // 这里可以清理插件创建的资源
    // 菜单项和路由会由插件管理器自动清理
    
    console.log('Example Plugin: Deactivation complete');
  },

  // 插件卸载时调用
  onUnload: async () => {
    console.log('Example Plugin: onUnload called');
    
    // 清理所有资源
    // 这是最后的清理机会
    
    console.log('Example Plugin: Unload complete');
  },
};

// 导出插件
export default ExamplePlugin;

// 也可以命名导出，提供更多灵活性
export { ExamplePlugin, ExamplePluginComponent };
