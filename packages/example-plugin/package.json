{"name": "example-plugin", "version": "1.0.0", "description": "Example plugin for the plugin playground", "type": "module", "main": "dist/example-plugin.js", "scripts": {"dev": "vite build --watch", "build": "tsc && vite build", "preview": "vite preview --port 3001", "serve": "node serve.js"}, "peerDependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^4.3.3", "typescript": "^5.8.3", "vite": "^5.4.10"}}