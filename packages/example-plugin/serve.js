import { createServer } from 'http';
import { readFile } from 'fs/promises';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const PORT = 3001;
const DIST_DIR = join(__dirname, 'dist');

const mimeTypes = {
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.html': 'text/html',
};

const server = createServer(async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    let filePath;
    
    if (req.url === '/') {
      // Serve a simple index page
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(`
        <html>
          <head><title>Plugin Server</title></head>
          <body>
            <h1>Plugin Server</h1>
            <p>Available files:</p>
            <ul>
              <li><a href="/dist/example-plugin.js">example-plugin.js</a></li>
              <li><a href="/dist/style.css">style.css</a></li>
            </ul>
          </body>
        </html>
      `);
      return;
    }
    
    if (req.url.startsWith('/dist/')) {
      filePath = join(DIST_DIR, req.url.replace('/dist/', ''));
    } else {
      res.writeHead(404);
      res.end('Not Found');
      return;
    }

    const content = await readFile(filePath);
    const ext = extname(filePath);
    const contentType = mimeTypes[ext] || 'application/octet-stream';

    res.writeHead(200, { 'Content-Type': contentType });
    res.end(content);
  } catch (error) {
    console.error('Error serving file:', error);
    res.writeHead(404);
    res.end('Not Found');
  }
});

server.listen(PORT, () => {
  console.log(`Plugin server running at http://localhost:${PORT}/`);
  console.log(`Plugin available at: http://localhost:${PORT}/dist/example-plugin.js`);
});
