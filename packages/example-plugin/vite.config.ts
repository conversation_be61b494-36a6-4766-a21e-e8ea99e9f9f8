import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@shared': path.resolve(__dirname, '../../shared'),
    },
  },
  build: {
    // 构建为库模式，输出 ESM 格式
    lib: {
      entry: path.resolve(__dirname, 'src/index.tsx'),
      name: 'ExamplePlugin',
      formats: ['es'],
      fileName: 'example-plugin',
    },
    rollupOptions: {
      // 确保 React 等共享依赖不被打包
      external: ['react', 'react-dom', 'react/jsx-runtime'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'react/jsx-runtime': 'jsxRuntime',
        },
      },
    },
    // 生成源码映射
    sourcemap: true,
    // 输出目录
    outDir: 'dist',
  },
  // 开发服务器配置（用于预览插件）
  server: {
    port: 3001,
    cors: true,
  },
})
