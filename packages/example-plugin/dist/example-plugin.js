import { jsxs as n, jsx as e } from "react/jsx-runtime";
import x, { useState as a, useEffect as f } from "react";
const p = () => {
  const [l, o] = a(0), [h, g] = a(""), [t, r] = a([]), [s, m] = a("");
  f(() => {
    g("Example Plugin loaded successfully!");
  }, []);
  const u = () => {
    s.trim() && (r([...t, s.trim()]), m(""));
  }, v = (i) => {
    r(t.filter((c, N) => N !== i));
  };
  return /* @__PURE__ */ n("div", { className: "example-plugin", children: [
    /* @__PURE__ */ n("div", { className: "plugin-header", children: [
      /* @__PURE__ */ e("h2", { children: "🔌 Example Plugin" }),
      /* @__PURE__ */ e("p", { className: "plugin-description", children: "This is a demonstration plugin that shows how plugins can be dynamically loaded and integrated into the main application." })
    ] }),
    /* @__PURE__ */ n("div", { className: "plugin-content", children: [
      /* @__PURE__ */ n("div", { className: "status-section", children: [
        /* @__PURE__ */ e("h3", { children: "Status" }),
        /* @__PURE__ */ e("p", { className: "status-message", children: h }),
        /* @__PURE__ */ n("p", { className: "plugin-info", children: [
          "Plugin ID: ",
          /* @__PURE__ */ e("code", { children: "example-plugin" }),
          /* @__PURE__ */ e("br", {}),
          "Version: ",
          /* @__PURE__ */ e("code", { children: "1.0.0" }),
          /* @__PURE__ */ e("br", {}),
          "React Version: ",
          /* @__PURE__ */ e("code", { children: x.version })
        ] })
      ] }),
      /* @__PURE__ */ n("div", { className: "counter-section", children: [
        /* @__PURE__ */ e("h3", { children: "Counter Demo" }),
        /* @__PURE__ */ n("div", { className: "counter", children: [
          /* @__PURE__ */ e("button", { onClick: () => o(l - 1), children: "-" }),
          /* @__PURE__ */ e("span", { className: "count-display", children: l }),
          /* @__PURE__ */ e("button", { onClick: () => o(l + 1), children: "+" })
        ] }),
        /* @__PURE__ */ e(
          "button",
          {
            className: "reset-button",
            onClick: () => o(0),
            children: "Reset"
          }
        )
      ] }),
      /* @__PURE__ */ n("div", { className: "todo-section", children: [
        /* @__PURE__ */ e("h3", { children: "Todo List Demo" }),
        /* @__PURE__ */ n("div", { className: "todo-input", children: [
          /* @__PURE__ */ e(
            "input",
            {
              type: "text",
              value: s,
              onChange: (i) => m(i.target.value),
              placeholder: "Enter a new todo...",
              onKeyPress: (i) => i.key === "Enter" && u()
            }
          ),
          /* @__PURE__ */ e("button", { onClick: u, children: "Add" })
        ] }),
        t.length > 0 && /* @__PURE__ */ e("ul", { className: "todo-list", children: t.map((i, c) => /* @__PURE__ */ n("li", { className: "todo-item", children: [
          /* @__PURE__ */ e("span", { className: "todo-text", children: i }),
          /* @__PURE__ */ e(
            "button",
            {
              className: "remove-button",
              onClick: () => v(c),
              children: "×"
            }
          )
        ] }, c)) }),
        t.length === 0 && /* @__PURE__ */ e("p", { className: "empty-todos", children: "No todos yet. Add one above!" })
      ] }),
      /* @__PURE__ */ n("div", { className: "features-section", children: [
        /* @__PURE__ */ e("h3", { children: "Plugin Features" }),
        /* @__PURE__ */ n("ul", { className: "features-list", children: [
          /* @__PURE__ */ e("li", { children: "✅ Dynamic loading from URL" }),
          /* @__PURE__ */ e("li", { children: "✅ React components with state" }),
          /* @__PURE__ */ e("li", { children: "✅ Shared React dependency (no duplication)" }),
          /* @__PURE__ */ e("li", { children: "✅ ESM module format" }),
          /* @__PURE__ */ e("li", { children: "✅ TypeScript support" }),
          /* @__PURE__ */ e("li", { children: "✅ Hot reload during development" }),
          /* @__PURE__ */ e("li", { children: "✅ Plugin lifecycle management" })
        ] })
      ] })
    ] })
  ] });
}, d = {
  metadata: {
    id: "example-plugin",
    name: "Example Plugin",
    version: "1.0.0",
    description: "A demonstration plugin showing various features of the plugin system",
    author: "Plugin Playground Team",
    minAppVersion: "1.0.0"
  },
  // 插件主组件
  component: p,
  // 插件激活时调用
  onActivate: async (l) => {
    console.log("Example Plugin: onActivate called"), l.registerMenuItem({
      id: "example-plugin-menu",
      label: "🔌 Example Plugin",
      href: "/plugin/example"
    }), l.registerRoute({
      path: "/plugin/example",
      component: p
    }), l.showNotification(
      `${d.metadata.name} has been activated!`,
      "success"
    ), l.setAppState({
      [`plugin_${d.metadata.id}_active`]: !0,
      [`plugin_${d.metadata.id}_activatedAt`]: (/* @__PURE__ */ new Date()).toISOString()
    }), console.log("Example Plugin: Activation complete");
  },
  // 插件停用时调用
  onDeactivate: async () => {
    console.log("Example Plugin: onDeactivate called"), console.log("Example Plugin: Deactivation complete");
  },
  // 插件卸载时调用
  onUnload: async () => {
    console.log("Example Plugin: onUnload called"), console.log("Example Plugin: Unload complete");
  }
};
export {
  d as ExamplePlugin,
  p as ExamplePluginComponent,
  d as default
};
//# sourceMappingURL=example-plugin.js.map
