{"version": 3, "file": "example-plugin.js", "sources": ["../src/ExamplePluginComponent.tsx", "../src/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './ExamplePlugin.css';\n\n/**\n * 示例插件组件\n */\nexport const ExamplePluginComponent: React.FC = () => {\n  const [count, setCount] = useState(0);\n  const [message, setMessage] = useState('');\n  const [todos, setTodos] = useState<string[]>([]);\n  const [newTodo, setNewTodo] = useState('');\n\n  useEffect(() => {\n    setMessage('Example Plugin loaded successfully!');\n  }, []);\n\n  const addTodo = () => {\n    if (newTodo.trim()) {\n      setTodos([...todos, newTodo.trim()]);\n      setNewTodo('');\n    }\n  };\n\n  const removeTodo = (index: number) => {\n    setTodos(todos.filter((_, i) => i !== index));\n  };\n\n  return (\n    <div className=\"example-plugin\">\n      <div className=\"plugin-header\">\n        <h2>🔌 Example Plugin</h2>\n        <p className=\"plugin-description\">\n          This is a demonstration plugin that shows how plugins can be dynamically loaded\n          and integrated into the main application.\n        </p>\n      </div>\n\n      <div className=\"plugin-content\">\n        {/* 状态消息 */}\n        <div className=\"status-section\">\n          <h3>Status</h3>\n          <p className=\"status-message\">{message}</p>\n          <p className=\"plugin-info\">\n            Plugin ID: <code>example-plugin</code><br />\n            Version: <code>1.0.0</code><br />\n            React Version: <code>{React.version}</code>\n          </p>\n        </div>\n\n        {/* 计数器功能 */}\n        <div className=\"counter-section\">\n          <h3>Counter Demo</h3>\n          <div className=\"counter\">\n            <button onClick={() => setCount(count - 1)}>-</button>\n            <span className=\"count-display\">{count}</span>\n            <button onClick={() => setCount(count + 1)}>+</button>\n          </div>\n          <button \n            className=\"reset-button\"\n            onClick={() => setCount(0)}\n          >\n            Reset\n          </button>\n        </div>\n\n        {/* Todo 列表功能 */}\n        <div className=\"todo-section\">\n          <h3>Todo List Demo</h3>\n          <div className=\"todo-input\">\n            <input\n              type=\"text\"\n              value={newTodo}\n              onChange={(e) => setNewTodo(e.target.value)}\n              placeholder=\"Enter a new todo...\"\n              onKeyPress={(e) => e.key === 'Enter' && addTodo()}\n            />\n            <button onClick={addTodo}>Add</button>\n          </div>\n          \n          {todos.length > 0 && (\n            <ul className=\"todo-list\">\n              {todos.map((todo, index) => (\n                <li key={index} className=\"todo-item\">\n                  <span className=\"todo-text\">{todo}</span>\n                  <button \n                    className=\"remove-button\"\n                    onClick={() => removeTodo(index)}\n                  >\n                    ×\n                  </button>\n                </li>\n              ))}\n            </ul>\n          )}\n          \n          {todos.length === 0 && (\n            <p className=\"empty-todos\">No todos yet. Add one above!</p>\n          )}\n        </div>\n\n        {/* 插件功能演示 */}\n        <div className=\"features-section\">\n          <h3>Plugin Features</h3>\n          <ul className=\"features-list\">\n            <li>✅ Dynamic loading from URL</li>\n            <li>✅ React components with state</li>\n            <li>✅ Shared React dependency (no duplication)</li>\n            <li>✅ ESM module format</li>\n            <li>✅ TypeScript support</li>\n            <li>✅ Hot reload during development</li>\n            <li>✅ Plugin lifecycle management</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport type { Plugin, PluginContext } from '@shared/plugin-types';\nimport { ExamplePluginComponent } from './ExamplePluginComponent';\n\n/**\n * 示例插件实现\n */\nconst ExamplePlugin: Plugin = {\n  metadata: {\n    id: 'example-plugin',\n    name: 'Example Plugin',\n    version: '1.0.0',\n    description: 'A demonstration plugin showing various features of the plugin system',\n    author: 'Plugin Playground Team',\n    minAppVersion: '1.0.0',\n  },\n\n  // 插件主组件\n  component: ExamplePluginComponent,\n\n  // 插件激活时调用\n  onActivate: async (context: PluginContext) => {\n    console.log('Example Plugin: onActivate called');\n    \n    // 注册菜单项\n    context.registerMenuItem({\n      id: 'example-plugin-menu',\n      label: '🔌 Example Plugin',\n      href: '/plugin/example',\n    });\n\n    // 注册路由\n    context.registerRoute({\n      path: '/plugin/example',\n      component: ExamplePluginComponent,\n    });\n\n    // 显示激活通知\n    context.showNotification(\n      `${ExamplePlugin.metadata.name} has been activated!`,\n      'success'\n    );\n\n    // 设置插件状态到应用状态中\n    context.setAppState({\n      [`plugin_${ExamplePlugin.metadata.id}_active`]: true,\n      [`plugin_${ExamplePlugin.metadata.id}_activatedAt`]: new Date().toISOString(),\n    });\n\n    console.log('Example Plugin: Activation complete');\n  },\n\n  // 插件停用时调用\n  onDeactivate: async () => {\n    console.log('Example Plugin: onDeactivate called');\n    \n    // 这里可以清理插件创建的资源\n    // 菜单项和路由会由插件管理器自动清理\n    \n    console.log('Example Plugin: Deactivation complete');\n  },\n\n  // 插件卸载时调用\n  onUnload: async () => {\n    console.log('Example Plugin: onUnload called');\n    \n    // 清理所有资源\n    // 这是最后的清理机会\n    \n    console.log('Example Plugin: Unload complete');\n  },\n};\n\n// 导出插件\nexport default ExamplePlugin;\n\n// 也可以命名导出，提供更多灵活性\nexport { ExamplePlugin, ExamplePluginComponent };\n"], "names": ["ExamplePluginComponent", "count", "setCount", "useState", "message", "setMessage", "todos", "<PERSON><PERSON><PERSON><PERSON>", "newTodo", "setNewTodo", "useEffect", "addTodo", "removeTodo", "index", "_", "i", "jsxs", "jsx", "React", "e", "todo", "ExamplePlugin", "context"], "mappings": ";;AAMO,MAAMA,IAAmC,MAAM;AACpD,QAAM,CAACC,GAAOC,CAAQ,IAAIC,EAAS,CAAC,GAC9B,CAACC,GAASC,CAAU,IAAIF,EAAS,EAAE,GACnC,CAACG,GAAOC,CAAQ,IAAIJ,EAAmB,CAAA,CAAE,GACzC,CAACK,GAASC,CAAU,IAAIN,EAAS,EAAE;AAEzC,EAAAO,EAAU,MAAM;AACd,IAAAL,EAAW,qCAAqC;AAAA,EAClD,GAAG,CAAA,CAAE;AAEL,QAAMM,IAAU,MAAM;AACpB,IAAIH,EAAQ,WACVD,EAAS,CAAC,GAAGD,GAAOE,EAAQ,KAAA,CAAM,CAAC,GACnCC,EAAW,EAAE;AAAA,EAEjB,GAEMG,IAAa,CAACC,MAAkB;AACpC,IAAAN,EAASD,EAAM,OAAO,CAACQ,GAAGC,MAAMA,MAAMF,CAAK,CAAC;AAAA,EAC9C;AAEA,SACE,gBAAAG,EAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,IAAA,gBAAAA,EAAC,OAAA,EAAI,WAAU,iBACb,UAAA;AAAA,MAAA,gBAAAC,EAAC,QAAG,UAAA,oBAAA,CAAiB;AAAA,MACrB,gBAAAA,EAAC,KAAA,EAAE,WAAU,sBAAqB,UAAA,4HAAA,CAGlC;AAAA,IAAA,GACF;AAAA,IAEA,gBAAAD,EAAC,OAAA,EAAI,WAAU,kBAEb,UAAA;AAAA,MAAA,gBAAAA,EAAC,OAAA,EAAI,WAAU,kBACb,UAAA;AAAA,QAAA,gBAAAC,EAAC,QAAG,UAAA,SAAA,CAAM;AAAA,QACV,gBAAAA,EAAC,KAAA,EAAE,WAAU,kBAAkB,UAAAb,GAAQ;AAAA,QACvC,gBAAAY,EAAC,KAAA,EAAE,WAAU,eAAc,UAAA;AAAA,UAAA;AAAA,UACd,gBAAAC,EAAC,UAAK,UAAA,iBAAA,CAAc;AAAA,4BAAQ,MAAA,EAAG;AAAA,UAAE;AAAA,UACnC,gBAAAA,EAAC,UAAK,UAAA,QAAA,CAAK;AAAA,4BAAQ,MAAA,EAAG;AAAA,UAAE;AAAA,UAClB,gBAAAA,EAAC,QAAA,EAAM,UAAAC,EAAM,QAAA,CAAQ;AAAA,QAAA,EAAA,CACtC;AAAA,MAAA,GACF;AAAA,MAGA,gBAAAF,EAAC,OAAA,EAAI,WAAU,mBACb,UAAA;AAAA,QAAA,gBAAAC,EAAC,QAAG,UAAA,eAAA,CAAY;AAAA,QAChB,gBAAAD,EAAC,OAAA,EAAI,WAAU,WACb,UAAA;AAAA,UAAA,gBAAAC,EAAC,YAAO,SAAS,MAAMf,EAASD,IAAQ,CAAC,GAAG,UAAA,KAAC;AAAA,UAC7C,gBAAAgB,EAAC,QAAA,EAAK,WAAU,iBAAiB,UAAAhB,GAAM;AAAA,UACvC,gBAAAgB,EAAC,YAAO,SAAS,MAAMf,EAASD,IAAQ,CAAC,GAAG,UAAA,IAAA,CAAC;AAAA,QAAA,GAC/C;AAAA,QACA,gBAAAgB;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAU;AAAA,YACV,SAAS,MAAMf,EAAS,CAAC;AAAA,YAC1B,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAED,GACF;AAAA,MAGA,gBAAAc,EAAC,OAAA,EAAI,WAAU,gBACb,UAAA;AAAA,QAAA,gBAAAC,EAAC,QAAG,UAAA,iBAAA,CAAc;AAAA,QAClB,gBAAAD,EAAC,OAAA,EAAI,WAAU,cACb,UAAA;AAAA,UAAA,gBAAAC;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,MAAK;AAAA,cACL,OAAOT;AAAA,cACP,UAAU,CAACW,MAAMV,EAAWU,EAAE,OAAO,KAAK;AAAA,cAC1C,aAAY;AAAA,cACZ,YAAY,CAACA,MAAMA,EAAE,QAAQ,WAAWR,EAAA;AAAA,YAAQ;AAAA,UAAA;AAAA,UAElD,gBAAAM,EAAC,UAAA,EAAO,SAASN,GAAS,UAAA,MAAA,CAAG;AAAA,QAAA,GAC/B;AAAA,QAECL,EAAM,SAAS,KACd,gBAAAW,EAAC,QAAG,WAAU,aACX,UAAAX,EAAM,IAAI,CAACc,GAAMP,MAChB,gBAAAG,EAAC,MAAA,EAAe,WAAU,aACxB,UAAA;AAAA,UAAA,gBAAAC,EAAC,QAAA,EAAK,WAAU,aAAa,UAAAG,GAAK;AAAA,UAClC,gBAAAH;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAU;AAAA,cACV,SAAS,MAAML,EAAWC,CAAK;AAAA,cAChC,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,KAPOA,CAQT,CACD,GACH;AAAA,QAGDP,EAAM,WAAW,uBACf,KAAA,EAAE,WAAU,eAAc,UAAA,+BAAA,CAA4B;AAAA,MAAA,GAE3D;AAAA,MAGA,gBAAAU,EAAC,OAAA,EAAI,WAAU,oBACb,UAAA;AAAA,QAAA,gBAAAC,EAAC,QAAG,UAAA,kBAAA,CAAe;AAAA,QACnB,gBAAAD,EAAC,MAAA,EAAG,WAAU,iBACZ,UAAA;AAAA,UAAA,gBAAAC,EAAC,QAAG,UAAA,6BAAA,CAA0B;AAAA,UAC9B,gBAAAA,EAAC,QAAG,UAAA,gCAAA,CAA6B;AAAA,UACjC,gBAAAA,EAAC,QAAG,UAAA,6CAAA,CAA0C;AAAA,UAC9C,gBAAAA,EAAC,QAAG,UAAA,sBAAA,CAAmB;AAAA,UACvB,gBAAAA,EAAC,QAAG,UAAA,uBAAA,CAAoB;AAAA,UACxB,gBAAAA,EAAC,QAAG,UAAA,kCAAA,CAA+B;AAAA,UACnC,gBAAAA,EAAC,QAAG,UAAA,gCAAA,CAA6B;AAAA,QAAA,EAAA,CACnC;AAAA,MAAA,EAAA,CACF;AAAA,IAAA,EAAA,CACF;AAAA,EAAA,GACF;AAEJ,GC7GMI,IAAwB;AAAA,EAC5B,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,eAAe;AAAA,EAAA;AAAA;AAAA,EAIjB,WAAWrB;AAAA;AAAA,EAGX,YAAY,OAAOsB,MAA2B;AAC5C,YAAQ,IAAI,mCAAmC,GAG/CA,EAAQ,iBAAiB;AAAA,MACvB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP,GAGDA,EAAQ,cAAc;AAAA,MACpB,MAAM;AAAA,MACN,WAAWtB;AAAA,IAAA,CACZ,GAGDsB,EAAQ;AAAA,MACN,GAAGD,EAAc,SAAS,IAAI;AAAA,MAC9B;AAAA,IAAA,GAIFC,EAAQ,YAAY;AAAA,MAClB,CAAC,UAAUD,EAAc,SAAS,EAAE,SAAS,GAAG;AAAA,MAChD,CAAC,UAAUA,EAAc,SAAS,EAAE,cAAc,IAAG,oBAAI,KAAA,GAAO,YAAA;AAAA,IAAY,CAC7E,GAED,QAAQ,IAAI,qCAAqC;AAAA,EACnD;AAAA;AAAA,EAGA,cAAc,YAAY;AACxB,YAAQ,IAAI,qCAAqC,GAKjD,QAAQ,IAAI,uCAAuC;AAAA,EACrD;AAAA;AAAA,EAGA,UAAU,YAAY;AACpB,YAAQ,IAAI,iCAAiC,GAK7C,QAAQ,IAAI,iCAAiC;AAAA,EAC/C;AACF;"}