/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-nav {
  background: #1a1a1a;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
}

.nav-brand a {
  font-size: 1.5rem;
  font-weight: bold;
  color: #646cff;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-links a {
  color: #fff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-links a:hover {
  background-color: #333;
}

.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Home Page */
.home {
  text-align: left;
}

.home h1 {
  color: #646cff;
  margin-bottom: 1rem;
}

.features, .quick-start {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.features h2, .quick-start h2 {
  color: #fff;
  margin-bottom: 1rem;
}

.features ul, .quick-start ol {
  margin: 0;
  padding-left: 1.5rem;
}

.features li, .quick-start li {
  margin: 0.5rem 0;
  color: #ccc;
}

.quick-start code {
  background: #333;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #61dafb;
}

/* Plugin Management */
.plugin-management {
  text-align: left;
}

.plugin-management h2 {
  color: #646cff;
  margin-bottom: 2rem;
}

.plugin-management h3 {
  color: #fff;
  margin: 1.5rem 0 1rem 0;
}

.load-plugin-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.load-plugin-form {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.plugin-url-input {
  flex: 1;
  padding: 0.75rem;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 1rem;
}

.plugin-url-input:focus {
  outline: none;
  border-color: #646cff;
}

.load-plugin-button {
  padding: 0.75rem 1.5rem;
  background: #646cff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.load-plugin-button:hover:not(:disabled) {
  background: #535bf2;
}

.load-plugin-button:disabled {
  background: #555;
  cursor: not-allowed;
}

.error-message {
  color: #ff6b6b;
  padding: 0.5rem;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 4px;
  margin-top: 1rem;
}

.loaded-plugins-section {
  margin-bottom: 3rem;
}

.no-plugins {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.plugins-list {
  display: grid;
  gap: 1rem;
}

.plugin-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
}

.plugin-info {
  flex: 1;
}

.plugin-name {
  color: #fff;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.plugin-description {
  color: #ccc;
  margin: 0 0 0.5rem 0;
}

.plugin-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
}

.plugin-version {
  color: #61dafb;
}

.plugin-author {
  color: #888;
}

.plugin-actions {
  display: flex;
  gap: 0.5rem;
}

.plugin-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.activate-button {
  background: #4caf50;
  color: white;
}

.activate-button:hover {
  background: #45a049;
}

.deactivate-button {
  background: #ff9800;
  color: white;
}

.deactivate-button:hover {
  background: #e68900;
}

.unload-button {
  background: #f44336;
  color: white;
}

.unload-button:hover {
  background: #da190b;
}

/* Menu Items and Routes */
.plugin-menu-items-section,
.plugin-routes-section {
  margin-bottom: 2rem;
}

.menu-items-list,
.routes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item,
.route-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.menu-icon {
  font-size: 1.2rem;
}

.menu-label,
.route-path {
  color: #ccc;
}

.route-path {
  font-family: 'Courier New', monospace;
  color: #61dafb;
}

/* Notifications */
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.notification {
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

.notification-info {
  background: #2196f3;
  color: white;
}

.notification-success {
  background: #4caf50;
  color: white;
}

.notification-warning {
  background: #ff9800;
  color: white;
}

.notification-error {
  background: #f44336;
  color: white;
}

.notification-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-message {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  margin-left: 1rem;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.notification-close:hover {
  opacity: 1;
}

.clear-all-notifications {
  background: #333;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.clear-all-notifications:hover {
  background: #555;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Plugin Container */
.plugin-container {
  padding: 2rem;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  margin: 1rem 0;
}

.plugin-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333;
}

.plugin-title {
  color: #646cff;
  margin: 0;
}

.plugin-content {
  color: #ccc;
}
