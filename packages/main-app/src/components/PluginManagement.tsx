import React, { useState } from 'react';
import { usePluginManager } from '../plugins/PluginManager';

/**
 * 插件管理组件
 */
export const PluginManagement: React.FC = () => {
  const { state, loadPlugin, unloadPlugin, activatePlugin, deactivatePlugin } = usePluginManager();
  const [pluginUrl, setPluginUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLoadPlugin = async () => {
    if (!pluginUrl.trim()) {
      setError('Please enter a plugin URL');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await loadPlugin(pluginUrl);
      setPluginUrl('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load plugin');
    } finally {
      setLoading(false);
    }
  };

  const handleUnloadPlugin = async (pluginId: string) => {
    try {
      await unloadPlugin(pluginId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unload plugin');
    }
  };

  const handleActivatePlugin = async (pluginId: string) => {
    try {
      await activatePlugin(pluginId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to activate plugin');
    }
  };

  const handleDeactivatePlugin = async (pluginId: string) => {
    try {
      await deactivatePlugin(pluginId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate plugin');
    }
  };

  return (
    <div className="plugin-management">
      <h2>Plugin Management</h2>
      
      {/* 加载插件 */}
      <div className="load-plugin-section">
        <h3>Load Plugin</h3>
        <div className="load-plugin-form">
          <input
            type="text"
            value={pluginUrl}
            onChange={(e) => setPluginUrl(e.target.value)}
            placeholder="Enter plugin URL (e.g., http://localhost:3001/dist/plugin.js)"
            className="plugin-url-input"
          />
          <button
            onClick={handleLoadPlugin}
            disabled={loading}
            className="load-plugin-button"
          >
            {loading ? 'Loading...' : 'Load Plugin'}
          </button>
        </div>
        {error && <div className="error-message">{error}</div>}
      </div>

      {/* 已加载的插件列表 */}
      <div className="loaded-plugins-section">
        <h3>Loaded Plugins ({state.plugins.length})</h3>
        {state.plugins.length === 0 ? (
          <p className="no-plugins">No plugins loaded</p>
        ) : (
          <div className="plugins-list">
            {state.plugins.map((plugin) => (
              <div key={plugin.metadata.id} className="plugin-item">
                <div className="plugin-info">
                  <h4 className="plugin-name">{plugin.metadata.name}</h4>
                  <p className="plugin-description">{plugin.metadata.description}</p>
                  <div className="plugin-meta">
                    <span className="plugin-version">v{plugin.metadata.version}</span>
                    {plugin.metadata.author && (
                      <span className="plugin-author">by {plugin.metadata.author}</span>
                    )}
                  </div>
                </div>
                <div className="plugin-actions">
                  <button
                    onClick={() => handleActivatePlugin(plugin.metadata.id)}
                    className="activate-button"
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleDeactivatePlugin(plugin.metadata.id)}
                    className="deactivate-button"
                  >
                    Deactivate
                  </button>
                  <button
                    onClick={() => handleUnloadPlugin(plugin.metadata.id)}
                    className="unload-button"
                  >
                    Unload
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 插件菜单项 */}
      {state.menuItems.length > 0 && (
        <div className="plugin-menu-items-section">
          <h3>Plugin Menu Items ({state.menuItems.length})</h3>
          <ul className="menu-items-list">
            {state.menuItems.map((item) => (
              <li key={item.id} className="menu-item">
                {item.icon && <span className="menu-icon">{item.icon}</span>}
                <span className="menu-label">{item.label}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 插件路由 */}
      {state.routes.length > 0 && (
        <div className="plugin-routes-section">
          <h3>Plugin Routes ({state.routes.length})</h3>
          <ul className="routes-list">
            {state.routes.map((route) => (
              <li key={route.path} className="route-item">
                <span className="route-path">{route.path}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
