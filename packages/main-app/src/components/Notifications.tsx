import React from 'react';
import { usePluginManager } from '../plugins/PluginManager';

/**
 * 通知组件
 */
export const Notifications: React.FC = () => {
  const { state, clearNotifications } = usePluginManager();

  if (state.notifications.length === 0) {
    return null;
  }

  return (
    <div className="notifications-container">
      {state.notifications.map((notification) => (
        <div
          key={notification.id}
          className={`notification notification-${notification.type}`}
        >
          <div className="notification-content">
            <span className="notification-message">{notification.message}</span>
            <button
              className="notification-close"
              onClick={() => {
                // 移除单个通知的简单实现
                clearNotifications();
              }}
            >
              ×
            </button>
          </div>
        </div>
      ))}
      {state.notifications.length > 1 && (
        <button
          className="clear-all-notifications"
          onClick={clearNotifications}
        >
          Clear All
        </button>
      )}
    </div>
  );
};
