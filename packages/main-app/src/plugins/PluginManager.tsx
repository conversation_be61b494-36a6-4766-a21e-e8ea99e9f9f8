import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import type { 
  Plugin, 
  PluginContext, 
  MenuItem, 
  PluginRoute, 
  PluginConfig 
} from '@shared/plugin-types';
import { PluginLoader } from './PluginLoader';

interface PluginManagerState {
  plugins: Plugin[];
  menuItems: MenuItem[];
  routes: PluginRoute[];
  notifications: Array<{
    id: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    timestamp: number;
  }>;
  appState: any;
}

interface PluginManagerContextType {
  state: PluginManagerState;
  loadPlugin: (url: string) => Promise<void>;
  unloadPlugin: (pluginId: string) => Promise<void>;
  activatePlugin: (pluginId: string) => Promise<void>;
  deactivatePlugin: (pluginId: string) => Promise<void>;
  clearNotifications: () => void;
  setAppState: (state: any) => void;
}

const PluginManagerContext = createContext<PluginManagerContextType | null>(null);

export const usePluginManager = () => {
  const context = useContext(PluginManagerContext);
  if (!context) {
    throw new Error('usePluginManager must be used within a PluginManagerProvider');
  }
  return context;
};

interface PluginManagerProviderProps {
  children: ReactNode;
  initialPlugins?: PluginConfig[];
}

export const PluginManagerProvider: React.FC<PluginManagerProviderProps> = ({
  children,
  initialPlugins = [],
}) => {
  const navigate = useNavigate();
  const [pluginLoader] = useState(() => new PluginLoader());
  const [state, setState] = useState<PluginManagerState>({
    plugins: [],
    menuItems: [],
    routes: [],
    notifications: [],
    appState: {},
  });

  // 创建插件上下文
  const createPluginContext = useCallback((plugin: Plugin): PluginContext => {
    return {
      appVersion: '1.0.0',
      registerMenuItem: (item: MenuItem) => {
        setState(prev => ({
          ...prev,
          menuItems: [...prev.menuItems.filter(m => m.id !== item.id), item],
        }));
      },
      registerRoute: (route: PluginRoute) => {
        setState(prev => ({
          ...prev,
          routes: [...prev.routes.filter(r => r.path !== route.path), route],
        }));
      },
      showNotification: (message: string, type = 'info' as const) => {
        const notification = {
          id: `${Date.now()}-${Math.random()}`,
          message,
          type,
          timestamp: Date.now(),
        };
        setState(prev => ({
          ...prev,
          notifications: [...prev.notifications, notification],
        }));
        
        // 自动清除通知
        setTimeout(() => {
          setState(prev => ({
            ...prev,
            notifications: prev.notifications.filter(n => n.id !== notification.id),
          }));
        }, 5000);
      },
      getAppState: () => state.appState,
      setAppState: (newState: any) => {
        setState(prev => ({
          ...prev,
          appState: { ...prev.appState, ...newState },
        }));
      },
    };
  }, [state.appState]);

  // 加载插件
  const loadPlugin = useCallback(async (url: string) => {
    try {
      const plugin = await pluginLoader.loadPlugin(url);
      setState(prev => ({
        ...prev,
        plugins: [...prev.plugins.filter(p => p.metadata.id !== plugin.metadata.id), plugin],
      }));
    } catch (error) {
      console.error('Failed to load plugin:', error);
      throw error;
    }
  }, [pluginLoader]);

  // 卸载插件
  const unloadPlugin = useCallback(async (pluginId: string) => {
    try {
      await pluginLoader.unloadPlugin(pluginId);
      setState(prev => ({
        ...prev,
        plugins: prev.plugins.filter(p => p.metadata.id !== pluginId),
        menuItems: prev.menuItems.filter(item => !item.id.startsWith(pluginId)),
        routes: prev.routes.filter(route => !route.path.startsWith(`/plugin/${pluginId}`)),
      }));
    } catch (error) {
      console.error('Failed to unload plugin:', error);
      throw error;
    }
  }, [pluginLoader]);

  // 激活插件
  const activatePlugin = useCallback(async (pluginId: string) => {
    const plugin = pluginLoader.getPlugin(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} is not loaded`);
    }

    try {
      if (plugin.onActivate) {
        const context = createPluginContext(plugin);
        await plugin.onActivate(context);
      }
    } catch (error) {
      console.error(`Failed to activate plugin ${pluginId}:`, error);
      throw error;
    }
  }, [pluginLoader, createPluginContext]);

  // 停用插件
  const deactivatePlugin = useCallback(async (pluginId: string) => {
    const plugin = pluginLoader.getPlugin(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} is not loaded`);
    }

    try {
      if (plugin.onDeactivate) {
        await plugin.onDeactivate();
      }
      
      // 清理插件注册的菜单和路由
      setState(prev => ({
        ...prev,
        menuItems: prev.menuItems.filter(item => !item.id.startsWith(pluginId)),
        routes: prev.routes.filter(route => !route.path.startsWith(`/plugin/${pluginId}`)),
      }));
    } catch (error) {
      console.error(`Failed to deactivate plugin ${pluginId}:`, error);
      throw error;
    }
  }, [pluginLoader]);

  // 清除通知
  const clearNotifications = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: [],
    }));
  }, []);

  // 设置应用状态
  const setAppState = useCallback((newState: any) => {
    setState(prev => ({
      ...prev,
      appState: { ...prev.appState, ...newState },
    }));
  }, []);

  // 初始化插件
  useEffect(() => {
    const initializePlugins = async () => {
      for (const config of initialPlugins) {
        try {
          await loadPlugin(config.url);
          if (config.autoActivate) {
            // 从URL中提取插件ID（简单实现）
            const pluginId = config.url.split('/').pop()?.replace('.js', '') || '';
            if (pluginId) {
              await activatePlugin(pluginId);
            }
          }
        } catch (error) {
          console.error(`Failed to initialize plugin ${config.url}:`, error);
        }
      }
    };

    initializePlugins();
  }, [initialPlugins, loadPlugin, activatePlugin]);

  // 清理
  useEffect(() => {
    return () => {
      pluginLoader.cleanup();
    };
  }, [pluginLoader]);

  const contextValue: PluginManagerContextType = {
    state,
    loadPlugin,
    unloadPlugin,
    activatePlugin,
    deactivatePlugin,
    clearNotifications,
    setAppState,
  };

  return (
    <PluginManagerContext.Provider value={contextValue}>
      {children}
    </PluginManagerContext.Provider>
  );
};
