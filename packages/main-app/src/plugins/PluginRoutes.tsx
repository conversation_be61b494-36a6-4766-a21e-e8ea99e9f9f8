import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { usePluginManager } from './PluginManager';

/**
 * 插件路由组件
 * 动态渲染所有插件注册的路由
 */
export const PluginRoutes: React.FC = () => {
  const { state } = usePluginManager();

  return (
    <Routes>
      {state.routes.map((route) => (
        <Route
          key={route.path}
          path={route.path}
          element={<route.component />}
        />
      ))}
    </Routes>
  );
};

/**
 * 插件容器组件
 * 为插件提供统一的容器样式
 */
interface PluginContainerProps {
  children: React.ReactNode;
  pluginId?: string;
  title?: string;
}

export const PluginContainer: React.FC<PluginContainerProps> = ({
  children,
  pluginId,
  title,
}) => {
  return (
    <div className="plugin-container" data-plugin-id={pluginId}>
      {title && (
        <div className="plugin-header">
          <h2 className="plugin-title">{title}</h2>
        </div>
      )}
      <div className="plugin-content">
        {children}
      </div>
    </div>
  );
};
