import type { Plugin, PluginLoader as IPluginLoader, PluginConfig } from '@shared/plugin-types';

/**
 * 插件加载器实现
 */
export class PluginLoader implements IPluginLoader {
  private loadedPlugins = new Map<string, Plugin>();
  private pluginModules = new Map<string, any>();

  /**
   * 加载插件
   */
  async loadPlugin(url: string): Promise<Plugin> {
    try {
      console.log(`Loading plugin from: ${url}`);
      
      // 动态导入插件模块
      const module = await import(/* @vite-ignore */ url);
      
      // 获取插件实例
      const plugin: Plugin = module.default || module;
      
      if (!plugin || !plugin.metadata) {
        throw new Error('Invalid plugin: missing metadata');
      }

      // 检查插件是否已加载
      if (this.loadedPlugins.has(plugin.metadata.id)) {
        throw new Error(`Plugin ${plugin.metadata.id} is already loaded`);
      }

      // 验证插件元数据
      this.validatePlugin(plugin);

      // 存储插件
      this.loadedPlugins.set(plugin.metadata.id, plugin);
      this.pluginModules.set(plugin.metadata.id, module);

      console.log(`Plugin ${plugin.metadata.name} (${plugin.metadata.id}) loaded successfully`);
      
      return plugin;
    } catch (error) {
      console.error(`Failed to load plugin from ${url}:`, error);
      throw error;
    }
  }

  /**
   * 卸载插件
   */
  async unloadPlugin(pluginId: string): Promise<void> {
    const plugin = this.loadedPlugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin ${pluginId} is not loaded`);
    }

    try {
      // 调用插件的卸载生命周期方法
      if (plugin.onUnload) {
        await plugin.onUnload();
      }

      // 从内存中移除插件
      this.loadedPlugins.delete(pluginId);
      this.pluginModules.delete(pluginId);

      console.log(`Plugin ${plugin.metadata.name} (${pluginId}) unloaded successfully`);
    } catch (error) {
      console.error(`Failed to unload plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * 获取已加载的插件列表
   */
  getLoadedPlugins(): Plugin[] {
    return Array.from(this.loadedPlugins.values());
  }

  /**
   * 检查插件是否已加载
   */
  isPluginLoaded(pluginId: string): boolean {
    return this.loadedPlugins.has(pluginId);
  }

  /**
   * 获取特定插件
   */
  getPlugin(pluginId: string): Plugin | undefined {
    return this.loadedPlugins.get(pluginId);
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: Plugin): void {
    const { metadata } = plugin;
    
    if (!metadata.id || typeof metadata.id !== 'string') {
      throw new Error('Plugin metadata must have a valid id');
    }
    
    if (!metadata.name || typeof metadata.name !== 'string') {
      throw new Error('Plugin metadata must have a valid name');
    }
    
    if (!metadata.version || typeof metadata.version !== 'string') {
      throw new Error('Plugin metadata must have a valid version');
    }
  }

  /**
   * 批量加载插件
   */
  async loadPlugins(configs: PluginConfig[]): Promise<Plugin[]> {
    const results: Plugin[] = [];
    
    for (const config of configs) {
      try {
        const plugin = await this.loadPlugin(config.url);
        results.push(plugin);
      } catch (error) {
        console.error(`Failed to load plugin from ${config.url}:`, error);
      }
    }
    
    return results;
  }

  /**
   * 清理所有插件
   */
  async cleanup(): Promise<void> {
    const pluginIds = Array.from(this.loadedPlugins.keys());
    
    for (const pluginId of pluginIds) {
      try {
        await this.unloadPlugin(pluginId);
      } catch (error) {
        console.error(`Failed to cleanup plugin ${pluginId}:`, error);
      }
    }
  }
}
