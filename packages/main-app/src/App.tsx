import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { PluginManagerProvider } from './plugins/PluginManager';
import { PluginRoutes } from './plugins/PluginRoutes';
import { Notifications } from './components/Notifications';
import { PluginManagement } from './components/PluginManagement';
import './App.css';

const Home: React.FC = () => (
  <div className="home">
    <h1>Plugin Playground</h1>
    <p>Welcome to the React Plugin System Demo!</p>
    <p>This application demonstrates a dynamic plugin system where plugins can be loaded at runtime.</p>

    <div className="features">
      <h2>Features:</h2>
      <ul>
        <li>Dynamic plugin loading from URLs</li>
        <li>Plugin lifecycle management (activate/deactivate)</li>
        <li>Shared dependencies (React, React-DOM) to avoid duplication</li>
        <li>Plugin-registered routes and menu items</li>
        <li>ESM module format for both main app and plugins</li>
      </ul>
    </div>

    <div className="quick-start">
      <h2>Quick Start:</h2>
      <ol>
        <li>Go to <Link to="/plugins">Plugin Management</Link></li>
        <li>Load the example plugin: <code>http://localhost:3001/dist/example-plugin.js</code></li>
        <li>Activate the plugin to see it in action</li>
      </ol>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <PluginManagerProvider>
        <div className="app">
          <nav className="app-nav">
            <div className="nav-brand">
              <Link to="/">Plugin Playground</Link>
            </div>
            <div className="nav-links">
              <Link to="/">Home</Link>
              <Link to="/plugins">Plugins</Link>
            </div>
          </nav>

          <main className="app-main">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/plugins" element={<PluginManagement />} />
            </Routes>

            {/* 插件路由 */}
            <PluginRoutes />
          </main>

          {/* 通知系统 */}
          <Notifications />
        </div>
      </PluginManagerProvider>
    </Router>
  );
}

export default App;
