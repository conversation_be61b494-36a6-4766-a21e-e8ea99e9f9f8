# Plugin Playground

一个基于 React + Vite + TypeScript 的动态插件系统演示项目，支持运行时加载 ESM 插件，并确保共享依赖不重复打包。

## 🚀 特性

- ✅ **动态插件加载**: 支持从 URL 动态加载插件
- ✅ **ESM 模块格式**: 主应用和插件都使用 ESM 格式
- ✅ **共享依赖**: React、React-DOM 等库不会重复打包
- ✅ **TypeScript 支持**: 完整的 TypeScript 类型支持
- ✅ **插件生命周期**: 支持插件激活、停用、卸载
- ✅ **路由集成**: 插件可以注册自己的路由
- ✅ **菜单集成**: 插件可以注册菜单项
- ✅ **通知系统**: 插件可以显示通知消息
- ✅ **热重载**: 开发时支持热重载
- ✅ **Bun 包管理**: 使用 Bun 作为包管理器

## 📁 项目结构

```
plugin-playground/
├── packages/
│   ├── main-app/          # 主应用
│   │   ├── src/
│   │   │   ├── plugins/   # 插件系统核心
│   │   │   ├── components/# 应用组件
│   │   │   └── ...
│   │   └── package.json
│   └── example-plugin/    # 示例插件
│       ├── src/
│       ├── dist/          # 构建输出
│       └── package.json
├── shared/                # 共享类型定义
│   └── plugin-types.ts
└── package.json          # 根配置
```

## 🛠️ 开发环境要求

- Node.js 18+ 
- Bun (包管理器)

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装根依赖
bun install

# 安装主应用依赖
cd packages/main-app && bun install

# 安装插件依赖
cd packages/example-plugin && bun install
```

### 2. 构建插件

```bash
cd packages/example-plugin
bun run build
```

### 3. 启动服务

```bash
# 启动插件服务器 (端口 3001)
cd packages/example-plugin
bun run serve

# 新开终端，启动主应用 (端口 3000)
cd packages/main-app
bun run dev
```

### 4. 测试插件系统

1. 打开浏览器访问 http://localhost:3000
2. 点击导航栏的 "Plugins" 进入插件管理页面
3. 在插件 URL 输入框中输入: `http://localhost:3001/dist/example-plugin.js`
4. 点击 "Load Plugin" 加载插件
5. 点击 "Activate" 激活插件
6. 插件激活后会自动注册路由，可以通过 `/plugin/example` 访问

## 🔧 插件开发

### 插件接口

插件需要实现 `Plugin` 接口：

```typescript
interface Plugin {
  metadata: PluginMetadata;
  component?: ComponentType<any>;
  onActivate?: (context: PluginContext) => void | Promise<void>;
  onDeactivate?: () => void | Promise<void>;
  onUnload?: () => void | Promise<void>;
}
```

### 示例插件结构

```typescript
import React from 'react';
import type { Plugin } from '@shared/plugin-types';
import { MyPluginComponent } from './MyPluginComponent';

const MyPlugin: Plugin = {
  metadata: {
    id: 'my-plugin',
    name: 'My Plugin',
    version: '1.0.0',
    description: 'My awesome plugin',
  },
  
  component: MyPluginComponent,
  
  onActivate: async (context) => {
    // 注册路由
    context.registerRoute({
      path: '/plugin/my-plugin',
      component: MyPluginComponent,
    });
    
    // 注册菜单
    context.registerMenuItem({
      id: 'my-plugin-menu',
      label: 'My Plugin',
      href: '/plugin/my-plugin',
    });
    
    // 显示通知
    context.showNotification('Plugin activated!', 'success');
  },
};

export default MyPlugin;
```

### 插件构建配置

插件的 `vite.config.ts` 需要配置为库模式，并将 React 等依赖设为外部依赖：

```typescript
export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.tsx',
      formats: ['es'],
      fileName: 'my-plugin',
    },
    rollupOptions: {
      external: ['react', 'react-dom', 'react/jsx-runtime'],
    },
  },
});
```

## 🧪 测试

运行测试脚本检查系统状态：

```bash
node test-plugin-system.js
```

## 📚 核心概念

### 插件加载器 (PluginLoader)

负责动态加载和管理插件：
- 使用 `import()` 动态导入插件模块
- 验证插件元数据
- 管理插件生命周期

### 插件管理器 (PluginManager)

提供插件系统的 React 上下文：
- 管理插件状态
- 提供插件 API (路由注册、菜单注册等)
- 处理通知系统

### 共享依赖处理

- 主应用将 React 等库作为全局依赖
- 插件构建时将这些库标记为外部依赖
- 运行时插件使用主应用提供的共享依赖

## 🔍 故障排除

### 插件加载失败

1. 检查插件 URL 是否可访问
2. 确认插件已正确构建
3. 检查浏览器控制台错误信息

### CORS 错误

确保插件服务器启用了 CORS：

```javascript
res.setHeader('Access-Control-Allow-Origin', '*');
```

### 依赖冲突

确保插件的 `vite.config.ts` 中正确配置了外部依赖。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
