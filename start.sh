#!/bin/bash

# Plugin Playground 启动脚本

echo "🚀 Starting Plugin Playground..."

# 检查是否安装了 bun
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Please install Bun first."
    echo "Visit: https://bun.sh/"
    exit 1
fi

# 检查是否已构建插件
if [ ! -f "packages/example-plugin/dist/example-plugin.js" ]; then
    echo "📦 Building example plugin..."
    cd packages/example-plugin
    bun run build
    cd ../..
fi

echo "🔌 Starting plugin server on port 3001..."
cd packages/example-plugin
bun run serve &
PLUGIN_PID=$!
cd ../..

# 等待插件服务器启动
sleep 2

echo "🌐 Starting main application on port 3000..."
cd packages/main-app
bun run dev &
MAIN_PID=$!
cd ..

echo ""
echo "✅ Plugin Playground is starting up!"
echo ""
echo "📋 URLs:"
echo "  Main App: http://localhost:3000"
echo "  Plugin Server: http://localhost:3001"
echo "  Plugin File: http://localhost:3001/dist/example-plugin.js"
echo ""
echo "🎯 Quick Test:"
echo "  1. Open http://localhost:3000"
echo "  2. Go to 'Plugins' page"
echo "  3. Load plugin: http://localhost:3001/dist/example-plugin.js"
echo "  4. Activate the plugin"
echo ""
echo "⏹️  To stop all servers, press Ctrl+C"

# 等待用户中断
trap "echo ''; echo '🛑 Stopping servers...'; kill $PLUGIN_PID $MAIN_PID 2>/dev/null; exit 0" INT

# 保持脚本运行
wait
