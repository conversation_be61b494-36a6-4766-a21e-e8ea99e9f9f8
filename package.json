{"name": "plugin-playground", "version": "1.0.0", "description": "A React application with plugin system using Vite and ESM", "type": "module", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "bun run --cwd packages/main-app dev", "build": "bun run build:main && bun run build:plugin", "build:main": "bun run --cwd packages/main-app build", "build:plugin": "bun run --cwd packages/example-plugin build", "preview": "bun run --cwd packages/main-app preview", "clean": "rm -rf packages/*/dist packages/*/node_modules node_modules"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}